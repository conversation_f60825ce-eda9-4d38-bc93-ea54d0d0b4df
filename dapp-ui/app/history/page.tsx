'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';
import { 
  ArrowUpIcon, 
  ArrowDownIcon, 
  ClockIcon, 
  CheckCircleIcon,
  XCircleIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { DTC_CONTRACT_CONFIG } from '../config/contract';
import { Transaction } from '../types';

export default function HistoryPage() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [userAddress, setUserAddress] = useState('');
  const [filter, setFilter] = useState<'all' | 'sent' | 'received' | 'mint' | 'burn'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentBalance, setCurrentBalance] = useState('0');

  useEffect(() => {
    loadTransactionHistory();
  }, []);

  const loadTransactionHistory = async () => {
    if (!window.ethereum) {
      setIsLoading(false);
      return;
    }

    try {
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const address = await signer.getAddress();
      setUserAddress(address);

      const contract = new ethers.Contract(
        DTC_CONTRACT_CONFIG.address,
        DTC_CONTRACT_CONFIG.abi,
        provider
      );

      // Get current balance
      const balance = await contract.balanceOf(address);
      setCurrentBalance(ethers.formatEther(balance));

      // Get transfer events
      const transferFilter = contract.filters.Transfer();
      const events = await contract.queryFilter(transferFilter, -10000); // Last 10k blocks

      const userTransactions: Transaction[] = [];

      for (const event of events) {
        if (event.args) {
          const [from, to, value] = event.args;
          const block = await event.getBlock();
          
          // Only include transactions involving the user
          if (from.toLowerCase() === address.toLowerCase() || to.toLowerCase() === address.toLowerCase()) {
            const transaction: Transaction = {
              hash: event.transactionHash,
              from: from,
              to: to,
              amount: ethers.formatEther(value),
              timestamp: block.timestamp * 1000,
              status: 'confirmed',
              type: from === ethers.ZeroAddress ? 'mint' : 
                    to === ethers.ZeroAddress ? 'burn' : 'transfer'
            };
            userTransactions.push(transaction);
          }
        }
      }

      // Sort by timestamp (newest first)
      userTransactions.sort((a, b) => b.timestamp - a.timestamp);
      setTransactions(userTransactions);

    } catch (error) {
      console.error('Error loading transaction history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getTransactionIcon = (transaction: Transaction) => {
    if (transaction.type === 'mint') {
      return <ArrowDownIcon className="h-5 w-5 text-green-500" />;
    }
    if (transaction.type === 'burn') {
      return <ArrowUpIcon className="h-5 w-5 text-red-500" />;
    }
    if (transaction.from.toLowerCase() === userAddress.toLowerCase()) {
      return <ArrowUpIcon className="h-5 w-5 text-red-500" />;
    }
    return <ArrowDownIcon className="h-5 w-5 text-green-500" />;
  };

  const getTransactionType = (transaction: Transaction) => {
    if (transaction.type === 'mint') return 'Minted';
    if (transaction.type === 'burn') return 'Burned';
    if (transaction.from.toLowerCase() === userAddress.toLowerCase()) return 'Sent';
    return 'Received';
  };

  const getTransactionAmount = (transaction: Transaction) => {
    const amount = parseFloat(transaction.amount);
    if (transaction.type === 'mint' || 
        (transaction.type === 'transfer' && transaction.to.toLowerCase() === userAddress.toLowerCase())) {
      return `+${amount.toFixed(2)}`;
    }
    return `-${amount.toFixed(2)}`;
  };

  const getStatusIcon = (status: Transaction['status']) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      case 'failed':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const filteredTransactions = transactions.filter(tx => {
    // Apply filter
    if (filter !== 'all') {
      if (filter === 'sent' && tx.from.toLowerCase() !== userAddress.toLowerCase()) return false;
      if (filter === 'received' && tx.to.toLowerCase() !== userAddress.toLowerCase()) return false;
      if (filter === 'mint' && tx.type !== 'mint') return false;
      if (filter === 'burn' && tx.type !== 'burn') return false;
    }

    // Apply search
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      return (
        tx.hash.toLowerCase().includes(search) ||
        tx.from.toLowerCase().includes(search) ||
        tx.to.toLowerCase().includes(search) ||
        tx.amount.includes(search)
      );
    }

    return true;
  });

  const exportToCSV = () => {
    const headers = ['Date', 'Time', 'Type', 'From', 'To', 'Amount', 'Hash', 'Status'];
    const csvData = filteredTransactions.map(tx => [
      formatDate(tx.timestamp),
      formatTime(tx.timestamp),
      getTransactionType(tx),
      tx.from,
      tx.to,
      tx.amount,
      tx.hash,
      tx.status
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dtc-transactions-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Transaction History
        </h1>
        <p className="text-gray-600">
          View all your DATACOIN token transactions and activity.
        </p>
      </div>

      {/* Balance Card */}
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg p-6 text-white mb-8">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-blue-100 text-sm">Current Balance</p>
            <p className="text-3xl font-bold">
              {parseFloat(currentBalance).toFixed(2)} DTC
            </p>
          </div>
          <div className="text-right">
            <p className="text-blue-100 text-sm">Total Transactions</p>
            <p className="text-2xl font-bold">
              {transactions.length}
            </p>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          {/* Filter Buttons */}
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'all', label: 'All' },
              { key: 'received', label: 'Received' },
              { key: 'sent', label: 'Sent' },
              { key: 'mint', label: 'Minted' },
              { key: 'burn', label: 'Burned' }
            ].map(({ key, label }) => (
              <button
                key={key}
                onClick={() => setFilter(key as any)}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  filter === key
                    ? 'bg-indigo-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {label}
              </button>
            ))}
          </div>

          {/* Search and Actions */}
          <div className="flex items-center space-x-4">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search transactions..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>
            <button
              onClick={loadTransactionHistory}
              className="p-2 text-gray-500 hover:text-gray-700"
              title="Refresh"
            >
              <ArrowPathIcon className="h-5 w-5" />
            </button>
            <button
              onClick={exportToCSV}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
            >
              Export CSV
            </button>
          </div>
        </div>
      </div>

      {/* Transaction List */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading transaction history...</p>
          </div>
        ) : filteredTransactions.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-600">No transactions found.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    From/To
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Hash
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTransactions.map((transaction, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getTransactionIcon(transaction)}
                        <span className="ml-2 text-sm font-medium text-gray-900">
                          {getTransactionType(transaction)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {transaction.type === 'mint' ? (
                        <span className="text-green-600">System Mint</span>
                      ) : transaction.type === 'burn' ? (
                        <span className="text-red-600">System Burn</span>
                      ) : transaction.from.toLowerCase() === userAddress.toLowerCase() ? (
                        <span>To: {formatAddress(transaction.to)}</span>
                      ) : (
                        <span>From: {formatAddress(transaction.from)}</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <span className={
                        transaction.type === 'mint' || 
                        (transaction.type === 'transfer' && transaction.to.toLowerCase() === userAddress.toLowerCase())
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }>
                        {getTransactionAmount(transaction)} DTC
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>
                        <div>{formatDate(transaction.timestamp)}</div>
                        <div className="text-gray-500">{formatTime(transaction.timestamp)}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getStatusIcon(transaction.status)}
                        <span className="ml-2 text-sm text-gray-900 capitalize">
                          {transaction.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                      <a
                        href={`http://127.0.0.1:8000/tx/${transaction.hash}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        {formatAddress(transaction.hash)}
                      </a>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
