'use client';

import { useState } from 'react';
import { ethers } from 'ethers';
import toast from 'react-hot-toast';
import { CreditCardIcon, ShieldCheckIcon, ClockIcon } from '@heroicons/react/24/outline';
import { PurchaseRequest, PaymentResult } from '../types';

export default function PurchasePage() {
  const [purchaseData, setPurchaseData] = useState<PurchaseRequest>({
    amount: '',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    holderName: '',
    email: ''
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);

  const handleInputChange = (field: keyof PurchaseRequest, value: string) => {
    setPurchaseData(prev => ({ ...prev, [field]: value }));
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const validateForm = () => {
    if (!purchaseData.amount || parseFloat(purchaseData.amount) <= 0) {
      toast.error('Please enter a valid amount');
      return false;
    }
    if (!purchaseData.cardNumber || purchaseData.cardNumber.replace(/\s/g, '').length < 16) {
      toast.error('Please enter a valid card number');
      return false;
    }
    if (!purchaseData.expiryDate || purchaseData.expiryDate.length < 5) {
      toast.error('Please enter a valid expiry date');
      return false;
    }
    if (!purchaseData.cvv || purchaseData.cvv.length < 3) {
      toast.error('Please enter a valid CVV');
      return false;
    }
    if (!purchaseData.holderName.trim()) {
      toast.error('Please enter the cardholder name');
      return false;
    }
    if (!purchaseData.email.includes('@')) {
      toast.error('Please enter a valid email address');
      return false;
    }
    return true;
  };

  const processPayment = async (): Promise<PaymentResult> => {
    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate 90% success rate
    const success = Math.random() > 0.1;
    
    if (success) {
      return {
        success: true,
        transactionId: 'txn_' + Math.random().toString(36).substr(2, 9)
      };
    } else {
      return {
        success: false,
        error: 'Payment declined. Please check your card details.'
      };
    }
  };

  const mintTokens = async (amount: string) => {
    if (!window.ethereum) {
      throw new Error('MetaMask not found');
    }

    const provider = new ethers.BrowserProvider(window.ethereum);
    const signer = await provider.getSigner();
    const userAddress = await signer.getAddress();

    // In a real implementation, this would be called by the backend
    // after successful payment verification
    toast.success(`${amount} DTC tokens will be minted to your address: ${userAddress}`);
  };

  const handlePurchase = async () => {
    if (!validateForm()) return;

    setIsProcessing(true);
    setCurrentStep(2);

    try {
      // Step 1: Process payment
      toast.loading('Processing payment...', { id: 'purchase' });
      const paymentResult = await processPayment();

      if (!paymentResult.success) {
        throw new Error(paymentResult.error || 'Payment failed');
      }

      setCurrentStep(3);
      toast.loading('Minting tokens...', { id: 'purchase' });

      // Step 2: Mint tokens
      await mintTokens(purchaseData.amount);

      setCurrentStep(4);
      toast.success('Purchase completed successfully!', { id: 'purchase' });

      // Reset form
      setPurchaseData({
        amount: '',
        cardNumber: '',
        expiryDate: '',
        cvv: '',
        holderName: '',
        email: ''
      });

    } catch (error: any) {
      toast.error(error.message, { id: 'purchase' });
      setCurrentStep(1);
    } finally {
      setIsProcessing(false);
    }
  };

  const steps = [
    { id: 1, name: 'Payment Details', icon: CreditCardIcon },
    { id: 2, name: 'Processing Payment', icon: ClockIcon },
    { id: 3, name: 'Minting Tokens', icon: ShieldCheckIcon },
    { id: 4, name: 'Complete', icon: ShieldCheckIcon }
  ];

  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Buy DATACOIN Tokens
        </h1>
        <p className="text-gray-600">
          Purchase DTC tokens with your credit card. Tokens will be minted directly to your wallet.
        </p>
      </div>

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, stepIdx) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                currentStep >= step.id 
                  ? 'bg-indigo-600 border-indigo-600 text-white' 
                  : 'border-gray-300 text-gray-500'
              }`}>
                <step.icon className="w-5 h-5" />
              </div>
              {stepIdx < steps.length - 1 && (
                <div className={`flex-1 h-0.5 mx-4 ${
                  currentStep > step.id ? 'bg-indigo-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-between mt-2">
          {steps.map((step) => (
            <div key={step.id} className="text-xs text-gray-500 text-center">
              {step.name}
            </div>
          ))}
        </div>
      </div>

      {currentStep === 1 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="space-y-6">
            {/* Amount */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Amount (DTC)
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={purchaseData.amount}
                  onChange={(e) => handleInputChange('amount', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  placeholder="Enter amount of DTC tokens"
                  min="1"
                  step="1"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <span className="text-gray-500 text-sm">DTC</span>
                </div>
              </div>
              {purchaseData.amount && (
                <p className="mt-1 text-sm text-gray-600">
                  Cost: ${(parseFloat(purchaseData.amount) * 1).toFixed(2)} USD (1 DTC = $1.00)
                </p>
              )}
            </div>

            {/* Card Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Card Number
              </label>
              <input
                type="text"
                value={purchaseData.cardNumber}
                onChange={(e) => handleInputChange('cardNumber', formatCardNumber(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="1234 5678 9012 3456"
                maxLength={19}
              />
            </div>

            {/* Expiry and CVV */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Expiry Date
                </label>
                <input
                  type="text"
                  value={purchaseData.expiryDate}
                  onChange={(e) => handleInputChange('expiryDate', formatExpiryDate(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  placeholder="MM/YY"
                  maxLength={5}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  CVV
                </label>
                <input
                  type="text"
                  value={purchaseData.cvv}
                  onChange={(e) => handleInputChange('cvv', e.target.value.replace(/\D/g, ''))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  placeholder="123"
                  maxLength={4}
                />
              </div>
            </div>

            {/* Cardholder Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cardholder Name
              </label>
              <input
                type="text"
                value={purchaseData.holderName}
                onChange={(e) => handleInputChange('holderName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="John Doe"
              />
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <input
                type="email"
                value={purchaseData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="<EMAIL>"
              />
            </div>

            <button
              onClick={handlePurchase}
              disabled={isProcessing}
              className="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-3 px-4 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isProcessing ? 'Processing...' : `Purchase ${purchaseData.amount || '0'} DTC`}
            </button>
          </div>
        </div>
      )}

      {currentStep > 1 && currentStep < 4 && (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {steps[currentStep - 1].name}
          </h3>
          <p className="text-gray-600">
            Please wait while we process your transaction...
          </p>
        </div>
      )}

      {currentStep === 4 && (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <ShieldCheckIcon className="w-6 h-6 text-green-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Purchase Complete!
          </h3>
          <p className="text-gray-600 mb-4">
            Your DTC tokens have been successfully minted to your wallet.
          </p>
          <button
            onClick={() => setCurrentStep(1)}
            className="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-md"
          >
            Make Another Purchase
          </button>
        </div>
      )}

      {/* Security Notice */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <ShieldCheckIcon className="h-5 w-5 text-blue-400 mt-0.5" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Secure Payment Processing
            </h3>
            <p className="mt-1 text-sm text-blue-700">
              Your payment information is encrypted and processed securely. 
              Tokens are minted directly to your connected wallet address.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
