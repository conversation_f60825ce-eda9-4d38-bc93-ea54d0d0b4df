export interface WalletState {
  account: string;
  balance: string;
  isConnected: boolean;
  isConnecting: boolean;
}

export interface Transaction {
  hash: string;
  from: string;
  to: string;
  amount: string;
  timestamp: number;
  status: 'pending' | 'confirmed' | 'failed';
  type: 'transfer' | 'mint' | 'burn' | 'purchase';
}

export interface PurchaseRequest {
  amount: string;
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  holderName: string;
  email: string;
}

export interface AdminAction {
  type: 'mint' | 'burn';
  address: string;
  amount: string;
  reason?: string;
}

export interface ContractConfig {
  address: string;
  abi: any[];
  network: {
    chainId: string;
    name: string;
    rpcUrl: string;
  };
}

export interface PaymentResult {
  success: boolean;
  transactionId?: string;
  error?: string;
}

export interface TokenStats {
  totalSupply: string;
  circulatingSupply: string;
  holders: number;
  transfers: number;
}
