import { ContractConfig } from '../types';

export const DTC_CONTRACT_CONFIG: ContractConfig = {
  address: "0xF19A2d609770c473a38b0547217ec60FF0AeF0aB",
  abi: [
    "function balanceOf(address) view returns (uint256)",
    "function transfer(address to, uint256 amount) returns (bool)",
    "function transferFrom(address from, address to, uint256 amount) returns (bool)",
    "function approve(address spender, uint256 amount) returns (bool)",
    "function allowance(address owner, address spender) view returns (uint256)",
    "function totalSupply() view returns (uint256)",
    "function name() view returns (string)",
    "function symbol() view returns (string)",
    "function decimals() view returns (uint8)",
    "function mint(address to, uint256 amount)",
    "function burn(uint256 amount)",
    "function burnFrom(address account, uint256 amount)",
    "function transferWithTracking(address to, uint256 amount) returns (bool)",
    "function reverseTransfer(address from, address to)",
    "function getLastTransferAmount(address to, address from) view returns (uint256)",
    "function owner() view returns (address)",
    "function transferOwnership(address newOwner)",
    "event Transfer(address indexed from, address indexed to, uint256 value)",
    "event Approval(address indexed owner, address indexed spender, uint256 value)"
  ],
  network: {
    chainId: "0x2328", // 9000 in hex
    name: "Ethermint",
    rpcUrl: "http://127.0.0.1:8545"
  }
};

export const NETWORK_CONFIG = {
  chainId: 9000,
  chainName: 'Ethermint',
  rpcUrls: ['http://127.0.0.1:8545'],
  nativeCurrency: {
    name: 'ETH',
    symbol: 'ETH',
    decimals: 18
  },
  blockExplorerUrls: ['http://127.0.0.1:8000']
};

export const ADMIN_ADDRESSES = [
  "******************************************", // Default owner
  // Add more admin addresses here
];
