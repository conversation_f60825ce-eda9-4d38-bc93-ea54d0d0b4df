'use client';

import { useWallet } from '../contexts/WalletContext';

export default function WalletConnection() {
  const { wallet, connectWallet, disconnectWallet, addTokenToWallet } = useWallet();

  if (!wallet.isConnected) {
    return (
      <button
        onClick={connectWallet}
        disabled={wallet.isConnecting}
        className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {wallet.isConnecting ? (
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Connecting...
          </div>
        ) : (
          'Connect Wallet'
        )}
      </button>
    );
  }

  return (
    <div className="flex items-center space-x-4">
      <div className="text-sm">
        <div className="font-medium text-gray-900">
          {wallet.account.slice(0, 6)}...{wallet.account.slice(-4)}
        </div>
        <div className="text-gray-500">
          {parseFloat(wallet.balance).toFixed(2)} DTC
        </div>
      </div>
      <div className="flex space-x-2">
        <button
          onClick={addTokenToWallet}
          className="bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded text-xs transition-colors"
          title="Add DTC token to MetaMask"
        >
          Add Token
        </button>
        <button
          onClick={disconnectWallet}
          className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-xs transition-colors"
          title="Disconnect wallet"
        >
          Disconnect
        </button>
      </div>
    </div>
  );
}
