'use client';

import { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { ethers } from 'ethers';
import toast from 'react-hot-toast';
import { WalletState } from '../types';
import { DTC_CONTRACT_CONFIG, NETWORK_CONFIG } from '../config/contract';

interface WalletContextType {
  wallet: WalletState;
  connectWallet: () => Promise<void>;
  disconnectWallet: () => void;
  switchNetwork: () => Promise<void>;
  addTokenToWallet: () => Promise<void>;
  refreshBalance: () => Promise<void>;
  getContract: () => ethers.Contract | null;
  getSigner: () => Promise<ethers.Signer | null>;
}

const WalletContext = createContext<WalletContextType | undefined>(undefined);

type WalletAction = 
  | { type: 'SET_CONNECTING'; payload: boolean }
  | { type: 'SET_CONNECTED'; payload: { account: string; balance: string } }
  | { type: 'SET_BALANCE'; payload: string }
  | { type: 'DISCONNECT' }
  | { type: 'SET_ERROR'; payload: string };

const initialState: WalletState = {
  account: '',
  balance: '0',
  isConnected: false,
  isConnecting: false,
};

function walletReducer(state: WalletState, action: WalletAction): WalletState {
  switch (action.type) {
    case 'SET_CONNECTING':
      return { ...state, isConnecting: action.payload };
    case 'SET_CONNECTED':
      return {
        ...state,
        account: action.payload.account,
        balance: action.payload.balance,
        isConnected: true,
        isConnecting: false,
      };
    case 'SET_BALANCE':
      return { ...state, balance: action.payload };
    case 'DISCONNECT':
      return initialState;
    default:
      return state;
  }
}

export function WalletProvider({ children }: { children: ReactNode }) {
  const [wallet, dispatch] = useReducer(walletReducer, initialState);

  useEffect(() => {
    checkConnection();
    setupEventListeners();

    return () => {
      removeEventListeners();
    };
  }, []);

  const checkConnection = async () => {
    if (!window.ethereum) return;

    try {
      const accounts = await window.ethereum.request({ method: 'eth_accounts' });
      if (accounts.length > 0) {
        await connectWallet();
      }
    } catch (error) {
      console.error('Error checking connection:', error);
    }
  };

  const setupEventListeners = () => {
    if (!window.ethereum) return;

    window.ethereum.on('accountsChanged', handleAccountsChanged);
    window.ethereum.on('chainChanged', handleChainChanged);
    window.ethereum.on('disconnect', handleDisconnect);
  };

  const removeEventListeners = () => {
    if (!window.ethereum) return;

    window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
    window.ethereum.removeListener('chainChanged', handleChainChanged);
    window.ethereum.removeListener('disconnect', handleDisconnect);
  };

  const handleAccountsChanged = (accounts: string[]) => {
    if (accounts.length === 0) {
      dispatch({ type: 'DISCONNECT' });
      toast.error('Wallet disconnected');
    } else {
      connectWallet();
    }
  };

  const handleChainChanged = (chainId: string) => {
    // Reload the page when chain changes to avoid state inconsistencies
    window.location.reload();
  };

  const handleDisconnect = () => {
    dispatch({ type: 'DISCONNECT' });
    toast.error('Wallet disconnected');
  };

  const connectWallet = async () => {
    if (!window.ethereum) {
      toast.error('MetaMask is not installed. Please install MetaMask to continue.');
      return;
    }

    dispatch({ type: 'SET_CONNECTING', payload: true });

    try {
      // Request account access
      await window.ethereum.request({ method: 'eth_requestAccounts' });
      
      // Switch to correct network
      await switchNetwork();
      
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const address = await signer.getAddress();

      // Get DTC balance
      const balance = await getTokenBalance(address);

      dispatch({ 
        type: 'SET_CONNECTED', 
        payload: { account: address, balance } 
      });

      toast.success('Wallet connected successfully!');
    } catch (error: any) {
      console.error('Connection error:', error);
      dispatch({ type: 'SET_CONNECTING', payload: false });
      
      if (error.code === 4001) {
        toast.error('Connection rejected by user');
      } else if (error.code === -32002) {
        toast.error('Connection request already pending');
      } else {
        toast.error('Failed to connect wallet: ' + error.message);
      }
    }
  };

  const disconnectWallet = () => {
    dispatch({ type: 'DISCONNECT' });
    toast.success('Wallet disconnected');
  };

  const switchNetwork = async () => {
    if (!window.ethereum) return;

    try {
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: DTC_CONTRACT_CONFIG.network.chainId }],
      });
    } catch (switchError: any) {
      // This error code indicates that the chain has not been added to MetaMask
      if (switchError.code === 4902) {
        try {
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [NETWORK_CONFIG],
          });
        } catch (addError: any) {
          throw new Error('Failed to add network: ' + addError.message);
        }
      } else {
        throw new Error('Failed to switch network: ' + switchError.message);
      }
    }
  };

  const addTokenToWallet = async () => {
    if (!window.ethereum) {
      toast.error('MetaMask not found');
      return;
    }

    try {
      await window.ethereum.request({
        method: 'wallet_watchAsset',
        params: {
          type: 'ERC20',
          options: {
            address: DTC_CONTRACT_CONFIG.address,
            symbol: 'DTC',
            decimals: 18,
            image: '', // Add token logo URL if available
          },
        },
      });
      toast.success('DTC token added to wallet!');
    } catch (error: any) {
      if (error.code === 4001) {
        toast.error('Token addition rejected by user');
      } else {
        toast.error('Failed to add token to wallet');
      }
    }
  };

  const getTokenBalance = async (address: string): Promise<string> => {
    try {
      const provider = new ethers.BrowserProvider(window.ethereum);
      const contract = new ethers.Contract(
        DTC_CONTRACT_CONFIG.address,
        DTC_CONTRACT_CONFIG.abi,
        provider
      );
      const balance = await contract.balanceOf(address);
      return ethers.formatEther(balance);
    } catch (error) {
      console.error('Error getting token balance:', error);
      return '0';
    }
  };

  const refreshBalance = async () => {
    if (!wallet.account) return;

    try {
      const balance = await getTokenBalance(wallet.account);
      dispatch({ type: 'SET_BALANCE', payload: balance });
    } catch (error) {
      console.error('Error refreshing balance:', error);
    }
  };

  const getContract = (): ethers.Contract | null => {
    if (!window.ethereum) return null;

    try {
      const provider = new ethers.BrowserProvider(window.ethereum);
      return new ethers.Contract(
        DTC_CONTRACT_CONFIG.address,
        DTC_CONTRACT_CONFIG.abi,
        provider
      );
    } catch (error) {
      console.error('Error creating contract instance:', error);
      return null;
    }
  };

  const getSigner = async (): Promise<ethers.Signer | null> => {
    if (!window.ethereum) return null;

    try {
      const provider = new ethers.BrowserProvider(window.ethereum);
      return await provider.getSigner();
    } catch (error) {
      console.error('Error getting signer:', error);
      return null;
    }
  };

  const value: WalletContextType = {
    wallet,
    connectWallet,
    disconnectWallet,
    switchNetwork,
    addTokenToWallet,
    refreshBalance,
    getContract,
    getSigner,
  };

  return (
    <WalletContext.Provider value={value}>
      {children}
    </WalletContext.Provider>
  );
}

export function useWallet() {
  const context = useContext(WalletContext);
  if (context === undefined) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
}
