'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';
import toast from 'react-hot-toast';
import { 
  PlusIcon, 
  MinusIcon, 
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  UsersIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import { DTC_CONTRACT_CONFIG, ADMIN_ADDRESSES } from '../config/contract';
import { AdminAction, TokenStats } from '../types';

export default function AdminPage() {
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userAddress, setUserAddress] = useState('');
  const [contractOwner, setContractOwner] = useState('');
  
  const [mintData, setMintData] = useState({
    address: '',
    amount: '',
    reason: ''
  });
  
  const [burnData, setBurnData] = useState({
    address: '',
    amount: '',
    reason: ''
  });

  const [stats, setStats] = useState<TokenStats>({
    totalSupply: '0',
    circulatingSupply: '0',
    holders: 0,
    transfers: 0
  });

  const [recentActions, setRecentActions] = useState<AdminAction[]>([]);

  useEffect(() => {
    checkAdminAccess();
  }, []);

  const checkAdminAccess = async () => {
    if (!window.ethereum) {
      setIsLoading(false);
      return;
    }

    try {
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const address = await signer.getAddress();
      setUserAddress(address);

      const contract = new ethers.Contract(
        DTC_CONTRACT_CONFIG.address,
        DTC_CONTRACT_CONFIG.abi,
        provider
      );

      // Get contract owner
      const owner = await contract.owner();
      setContractOwner(owner);

      // Check if user is admin
      const isUserAdmin = ADMIN_ADDRESSES.includes(address) || 
                         address.toLowerCase() === owner.toLowerCase();
      setIsAdmin(isUserAdmin);

      if (isUserAdmin) {
        await loadStats();
      }

    } catch (error) {
      console.error('Error checking admin access:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const provider = new ethers.BrowserProvider(window.ethereum);
      const contract = new ethers.Contract(
        DTC_CONTRACT_CONFIG.address,
        DTC_CONTRACT_CONFIG.abi,
        provider
      );

      const totalSupply = await contract.totalSupply();
      
      setStats({
        totalSupply: ethers.formatEther(totalSupply),
        circulatingSupply: ethers.formatEther(totalSupply),
        holders: 0, // Would need separate tracking
        transfers: 0 // Would need separate tracking
      });

    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const validateAddress = (address: string) => {
    if (!address.trim()) {
      toast.error('Please enter an address');
      return false;
    }
    if (!ethers.isAddress(address)) {
      toast.error('Please enter a valid Ethereum address');
      return false;
    }
    return true;
  };

  const validateAmount = (amount: string) => {
    if (!amount || parseFloat(amount) <= 0) {
      toast.error('Please enter a valid amount');
      return false;
    }
    return true;
  };

  const handleMint = async () => {
    if (!validateAddress(mintData.address) || !validateAmount(mintData.amount)) {
      return;
    }

    try {
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const contract = new ethers.Contract(
        DTC_CONTRACT_CONFIG.address,
        DTC_CONTRACT_CONFIG.abi,
        signer
      );

      toast.loading('Minting tokens...', { id: 'mint' });

      const tx = await contract.mint(
        mintData.address,
        ethers.parseEther(mintData.amount)
      );

      await tx.wait();

      toast.success(`Successfully minted ${mintData.amount} DTC to ${mintData.address}`, { id: 'mint' });

      // Add to recent actions
      const action: AdminAction = {
        type: 'mint',
        address: mintData.address,
        amount: mintData.amount,
        reason: mintData.reason
      };
      setRecentActions(prev => [action, ...prev.slice(0, 9)]);

      // Reset form and reload stats
      setMintData({ address: '', amount: '', reason: '' });
      await loadStats();

    } catch (error: any) {
      console.error('Mint error:', error);
      toast.error('Mint failed: ' + (error.reason || error.message), { id: 'mint' });
    }
  };

  const handleBurn = async () => {
    if (!validateAddress(burnData.address) || !validateAmount(burnData.amount)) {
      return;
    }

    try {
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const contract = new ethers.Contract(
        DTC_CONTRACT_CONFIG.address,
        DTC_CONTRACT_CONFIG.abi,
        signer
      );

      // Check if burning from user's own account or another account
      const userAddress = await signer.getAddress();
      
      toast.loading('Burning tokens...', { id: 'burn' });

      let tx;
      if (burnData.address.toLowerCase() === userAddress.toLowerCase()) {
        // Burn from own account
        tx = await contract.burn(ethers.parseEther(burnData.amount));
      } else {
        // Burn from another account (requires approval)
        tx = await contract.burnFrom(
          burnData.address,
          ethers.parseEther(burnData.amount)
        );
      }

      await tx.wait();

      toast.success(`Successfully burned ${burnData.amount} DTC from ${burnData.address}`, { id: 'burn' });

      // Add to recent actions
      const action: AdminAction = {
        type: 'burn',
        address: burnData.address,
        amount: burnData.amount,
        reason: burnData.reason
      };
      setRecentActions(prev => [action, ...prev.slice(0, 9)]);

      // Reset form and reload stats
      setBurnData({ address: '', amount: '', reason: '' });
      await loadStats();

    } catch (error: any) {
      console.error('Burn error:', error);
      toast.error('Burn failed: ' + (error.reason || error.message), { id: 'burn' });
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="max-w-2xl mx-auto text-center">
        <div className="bg-red-50 border border-red-200 rounded-lg p-8">
          <ExclamationTriangleIcon className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-red-800 mb-4">
            Access Denied
          </h1>
          <p className="text-red-700 mb-4">
            You don't have administrator privileges to access this page.
          </p>
          <div className="text-sm text-red-600">
            <p>Your address: {formatAddress(userAddress)}</p>
            <p>Contract owner: {formatAddress(contractOwner)}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          🛡️ Admin Panel
        </h1>
        <p className="text-gray-600">
          Manage DATACOIN token supply and monitor system statistics.
        </p>
      </div>

      {/* Admin Status */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-8">
        <div className="flex items-center">
          <ShieldCheckIcon className="h-5 w-5 text-green-400" />
          <div className="ml-3">
            <p className="text-sm font-medium text-green-800">
              Administrator Access Confirmed
            </p>
            <p className="text-sm text-green-700">
              Connected as: {formatAddress(userAddress)}
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <ChartBarIcon className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Supply</p>
              <p className="text-2xl font-semibold text-gray-900">
                {parseFloat(stats.totalSupply).toLocaleString()} DTC
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <UsersIcon className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Token Holders</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats.holders.toLocaleString()}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <CogIcon className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Transfers</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats.transfers.toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Admin Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Mint Tokens */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center mb-6">
            <PlusIcon className="h-6 w-6 text-green-600" />
            <h2 className="ml-2 text-xl font-semibold text-gray-900">Mint Tokens</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Recipient Address
              </label>
              <input
                type="text"
                value={mintData.address}
                onChange={(e) => setMintData(prev => ({ ...prev, address: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="0x..."
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Amount (DTC)
              </label>
              <input
                type="number"
                value={mintData.amount}
                onChange={(e) => setMintData(prev => ({ ...prev, amount: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="0.00"
                min="0"
                step="0.01"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason (Optional)
              </label>
              <input
                type="text"
                value={mintData.reason}
                onChange={(e) => setMintData(prev => ({ ...prev, reason: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="e.g., Token purchase, Reward distribution"
              />
            </div>
            
            <button
              onClick={handleMint}
              className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md font-medium"
            >
              Mint Tokens
            </button>
          </div>
        </div>

        {/* Burn Tokens */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center mb-6">
            <MinusIcon className="h-6 w-6 text-red-600" />
            <h2 className="ml-2 text-xl font-semibold text-gray-900">Burn Tokens</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Address to Burn From
              </label>
              <input
                type="text"
                value={burnData.address}
                onChange={(e) => setBurnData(prev => ({ ...prev, address: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="0x..."
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Amount (DTC)
              </label>
              <input
                type="number"
                value={burnData.amount}
                onChange={(e) => setBurnData(prev => ({ ...prev, amount: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="0.00"
                min="0"
                step="0.01"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason (Optional)
              </label>
              <input
                type="text"
                value={burnData.reason}
                onChange={(e) => setBurnData(prev => ({ ...prev, reason: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="e.g., Token buyback, Deflationary measure"
              />
            </div>
            
            <button
              onClick={handleBurn}
              className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md font-medium"
            >
              Burn Tokens
            </button>
          </div>
        </div>
      </div>

      {/* Recent Actions */}
      {recentActions.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Recent Admin Actions</h2>
          <div className="space-y-4">
            {recentActions.map((action, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {action.type === 'mint' ? (
                      <PlusIcon className="h-5 w-5 text-green-500" />
                    ) : (
                      <MinusIcon className="h-5 w-5 text-red-500" />
                    )}
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">
                      {action.type === 'mint' ? 'Minted' : 'Burned'} {action.amount} DTC
                    </p>
                    <p className="text-sm text-gray-500">
                      {action.type === 'mint' ? 'To' : 'From'}: {formatAddress(action.address)}
                    </p>
                    {action.reason && (
                      <p className="text-xs text-gray-400">
                        Reason: {action.reason}
                      </p>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-gray-500">
                    Just now
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Warning */}
      <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Important Notice
            </h3>
            <p className="mt-1 text-sm text-yellow-700">
              Admin actions are irreversible and directly affect the token supply. 
              Please double-check all addresses and amounts before proceeding.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
