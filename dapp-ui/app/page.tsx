'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';
import Link from 'next/link';
import {
  CreditCardIcon,
  ArrowsRightLeftIcon,
  ClockIcon,
  CogIcon,
  ChartBarIcon,
  UsersIcon
} from '@heroicons/react/24/outline';
import { DTC_CONTRACT_CONFIG } from './config/contract';
import { TokenStats } from './types';

export default function Dashboard() {
  const [stats, setStats] = useState<TokenStats>({
    totalSupply: '0',
    circulatingSupply: '0',
    holders: 0,
    transfers: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadTokenStats();
  }, []);

  const loadTokenStats = async () => {
    try {
      if (!window.ethereum) return;

      const provider = new ethers.BrowserProvider(window.ethereum);
      const contract = new ethers.Contract(
        DTC_CONTRACT_CONFIG.address,
        DTC_CONTRACT_CONFIG.abi,
        provider
      );

      const totalSupply = await contract.totalSupply();

      setStats({
        totalSupply: ethers.formatEther(totalSupply),
        circulatingSupply: ethers.formatEther(totalSupply), // For now, same as total
        holders: 0, // Would need to track this separately
        transfers: 0 // Would need to track this separately
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const quickActions = [
    {
      name: 'Buy Coins',
      description: 'Purchase DTC tokens with credit card',
      href: '/purchase',
      icon: CreditCardIcon,
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      name: 'Transfer',
      description: 'Send DTC tokens to another address',
      href: '/transfer',
      icon: ArrowsRightLeftIcon,
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      name: 'History',
      description: 'View your transaction history',
      href: '/history',
      icon: ClockIcon,
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    {
      name: 'Admin Panel',
      description: 'Manage token supply (Admin only)',
      href: '/admin',
      icon: CogIcon,
      color: 'bg-red-500 hover:bg-red-600'
    }
  ];

  const statCards = [
    {
      name: 'Total Supply',
      value: parseFloat(stats.totalSupply).toLocaleString(),
      unit: 'DTC',
      icon: ChartBarIcon,
      color: 'text-blue-600'
    },
    {
      name: 'Circulating Supply',
      value: parseFloat(stats.circulatingSupply).toLocaleString(),
      unit: 'DTC',
      icon: ChartBarIcon,
      color: 'text-green-600'
    },
    {
      name: 'Token Holders',
      value: stats.holders.toLocaleString(),
      unit: 'addresses',
      icon: UsersIcon,
      color: 'text-purple-600'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          🪙 DATACOIN Dashboard
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Welcome to the DATACOIN ecosystem. Buy, transfer, and manage your DTC tokens
          with our comprehensive decentralized application.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {statCards.map((stat) => (
          <div key={stat.name} className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <stat.icon className={`h-8 w-8 ${stat.color}`} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                <div className="flex items-baseline">
                  <p className="text-2xl font-semibold text-gray-900">
                    {isLoading ? '...' : stat.value}
                  </p>
                  <p className="ml-2 text-sm text-gray-500">{stat.unit}</p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickActions.map((action) => (
            <Link
              key={action.name}
              href={action.href}
              className="group relative bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200"
            >
              <div className="p-6">
                <div className={`inline-flex p-3 rounded-lg ${action.color} text-white mb-4`}>
                  <action.icon className="h-6 w-6" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {action.name}
                </h3>
                <p className="text-gray-600 text-sm">
                  {action.description}
                </p>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Contract Info */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Contract Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700">Contract Address:</span>
            <p className="text-gray-600 font-mono break-all">
              {DTC_CONTRACT_CONFIG.address}
            </p>
          </div>
          <div>
            <span className="font-medium text-gray-700">Network:</span>
            <p className="text-gray-600">
              {DTC_CONTRACT_CONFIG.network.name} (Chain ID: {parseInt(DTC_CONTRACT_CONFIG.network.chainId, 16)})
            </p>
          </div>
          <div>
            <span className="font-medium text-gray-700">Token Symbol:</span>
            <p className="text-gray-600">DTC</p>
          </div>
          <div>
            <span className="font-medium text-gray-700">Decimals:</span>
            <p className="text-gray-600">18</p>
          </div>
        </div>
      </div>
    </div>
  );
}
