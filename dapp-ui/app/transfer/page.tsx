'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';
import toast from 'react-hot-toast';
import { ArrowRightIcon, ClockIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { DTC_CONTRACT_CONFIG } from '../config/contract';

interface TransferState {
  recipient: string;
  amount: string;
  isProcessing: boolean;
  userBalance: string;
  userAddress: string;
}

export default function TransferPage() {
  const [transferState, setTransferState] = useState<TransferState>({
    recipient: '',
    amount: '',
    isProcessing: false,
    userBalance: '0',
    userAddress: ''
  });

  const [recentTransfers, setRecentTransfers] = useState<any[]>([]);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    if (!window.ethereum) return;

    try {
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const address = await signer.getAddress();

      const contract = new ethers.Contract(
        DTC_CONTRACT_CONFIG.address,
        DTC_CONTRACT_CONFIG.abi,
        provider
      );

      const balance = await contract.balanceOf(address);

      setTransferState(prev => ({
        ...prev,
        userAddress: address,
        userBalance: ethers.formatEther(balance)
      }));

    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const validateTransfer = () => {
    if (!transferState.recipient.trim()) {
      toast.error('Please enter a recipient address');
      return false;
    }

    if (!ethers.isAddress(transferState.recipient)) {
      toast.error('Please enter a valid Ethereum address');
      return false;
    }

    if (!transferState.amount || parseFloat(transferState.amount) <= 0) {
      toast.error('Please enter a valid amount');
      return false;
    }

    if (parseFloat(transferState.amount) > parseFloat(transferState.userBalance)) {
      toast.error('Insufficient balance');
      return false;
    }

    if (transferState.recipient.toLowerCase() === transferState.userAddress.toLowerCase()) {
      toast.error('Cannot transfer to yourself');
      return false;
    }

    return true;
  };

  const handleTransfer = async () => {
    if (!validateTransfer()) return;

    setTransferState(prev => ({ ...prev, isProcessing: true }));

    try {
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const contract = new ethers.Contract(
        DTC_CONTRACT_CONFIG.address,
        DTC_CONTRACT_CONFIG.abi,
        signer
      );

      toast.loading('Initiating transfer...', { id: 'transfer' });

      const tx = await contract.transfer(
        transferState.recipient,
        ethers.parseEther(transferState.amount)
      );

      toast.loading('Waiting for confirmation...', { id: 'transfer' });

      const receipt = await tx.wait();

      toast.success('Transfer completed successfully!', { id: 'transfer' });

      // Add to recent transfers
      const newTransfer = {
        hash: receipt.hash,
        to: transferState.recipient,
        amount: transferState.amount,
        timestamp: Date.now(),
        status: 'confirmed'
      };

      setRecentTransfers(prev => [newTransfer, ...prev.slice(0, 4)]);

      // Reset form and reload balance
      setTransferState(prev => ({
        ...prev,
        recipient: '',
        amount: ''
      }));

      await loadUserData();

    } catch (error: any) {
      console.error('Transfer error:', error);
      toast.error('Transfer failed: ' + (error.reason || error.message), { id: 'transfer' });
    } finally {
      setTransferState(prev => ({ ...prev, isProcessing: false }));
    }
  };

  const setMaxAmount = () => {
    setTransferState(prev => ({ ...prev, amount: prev.userBalance }));
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Transfer DTC Tokens
        </h1>
        <p className="text-gray-600">
          Send DATACOIN tokens to any Ethereum address on the Ethermint network.
        </p>
      </div>

      {/* Balance Card */}
      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white mb-8">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-indigo-100 text-sm">Your Balance</p>
            <p className="text-3xl font-bold">
              {parseFloat(transferState.userBalance).toFixed(2)} DTC
            </p>
          </div>
          <div className="text-right">
            <p className="text-indigo-100 text-sm">Address</p>
            <p className="text-sm font-mono">
              {transferState.userAddress ? formatAddress(transferState.userAddress) : '...'}
            </p>
          </div>
        </div>
      </div>

      {/* Transfer Form */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Send Tokens</h2>
        
        <div className="space-y-6">
          {/* Recipient Address */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Recipient Address
            </label>
            <input
              type="text"
              value={transferState.recipient}
              onChange={(e) => setTransferState(prev => ({ ...prev, recipient: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="0x..."
              disabled={transferState.isProcessing}
            />
          </div>

          {/* Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Amount (DTC)
            </label>
            <div className="relative">
              <input
                type="number"
                value={transferState.amount}
                onChange={(e) => setTransferState(prev => ({ ...prev, amount: e.target.value }))}
                className="w-full px-3 py-2 pr-16 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="0.00"
                min="0"
                step="0.01"
                disabled={transferState.isProcessing}
              />
              <button
                type="button"
                onClick={setMaxAmount}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm text-indigo-600 hover:text-indigo-500"
                disabled={transferState.isProcessing}
              >
                MAX
              </button>
            </div>
            {transferState.amount && (
              <p className="mt-1 text-sm text-gray-600">
                Remaining balance: {(parseFloat(transferState.userBalance) - parseFloat(transferState.amount || '0')).toFixed(2)} DTC
              </p>
            )}
          </div>

          {/* Transfer Button */}
          <button
            onClick={handleTransfer}
            disabled={transferState.isProcessing || !transferState.recipient || !transferState.amount}
            className="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-3 px-4 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {transferState.isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing...
              </>
            ) : (
              <>
                <ArrowRightIcon className="h-4 w-4 mr-2" />
                Send {transferState.amount || '0'} DTC
              </>
            )}
          </button>
        </div>
      </div>

      {/* Recent Transfers */}
      {recentTransfers.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Recent Transfers</h2>
          <div className="space-y-4">
            {recentTransfers.map((transfer, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {transfer.status === 'confirmed' ? (
                      <CheckCircleIcon className="h-5 w-5 text-green-500" />
                    ) : (
                      <ClockIcon className="h-5 w-5 text-yellow-500" />
                    )}
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">
                      To: {formatAddress(transfer.to)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatTime(transfer.timestamp)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    -{transfer.amount} DTC
                  </p>
                  <p className="text-xs text-gray-500">
                    {transfer.status}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Help Section */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 mb-2">
          Transfer Guidelines
        </h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Ensure the recipient address is correct - transactions cannot be reversed</li>
          <li>• Transfers are processed on the Ethermint network</li>
          <li>• Small gas fees may apply for network processing</li>
          <li>• Transfers are typically confirmed within a few seconds</li>
        </ul>
      </div>
    </div>
  );
}
