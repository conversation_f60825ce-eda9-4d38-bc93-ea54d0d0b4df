{"name": "datacoin-api-server", "version": "1.0.0", "description": "API server for DATACOIN token issuance and payment processing", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "ethers": "^6.15.0", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "rate-limiter-flexible": "^3.0.8", "winston": "^3.11.0", "express-validator": "^7.0.1", "stripe": "^14.7.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["datacoin", "token", "api", "blockchain", "payment"], "author": "DATACOIN Team", "license": "MIT"}